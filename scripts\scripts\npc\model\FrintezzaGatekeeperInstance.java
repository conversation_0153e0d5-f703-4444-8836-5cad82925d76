package npc.model;

// import instances.Frintezza; // DISABLED - Using Epic Boss System
import l2mv.gameserver.model.Player;
import l2mv.gameserver.model.entity.Reflection;
import l2mv.gameserver.model.instances.NpcInstance;
import l2mv.gameserver.templates.npc.NpcTemplate;
import l2mv.gameserver.utils.ReflectionUtils;

/**
 * <AUTHOR>
 */

public final class FrintezzaGatekeeperInstance extends NpcInstance
{
	private static final int INSTANCE_ID = 136;

	public FrintezzaGatekeeperInstance(int objectId, NpcTemplate template)
	{
		super(objectId, template);
	}

	@Override
	public void onBypassFeedback(Player player, String command)
	{
		if (!canBypassCheck(player, this))
		{
			return;
		}

		if (command.equalsIgnoreCase("request_frintezza"))
		{
			Reflection r = player.getActiveReflection();
			if (r != null)
			{
				if (player.canReenterInstance(INSTANCE_ID))
				{
					player.teleToLocation(r.getTeleportLoc(), r);
				}
			}
			else if (player.canEnterInstance(INSTANCE_ID))
			{
				// DISABLED: Frintezza Instance System - Now using Epic Boss System
				player.sendMessage("Frintezza Instance is disabled. Use Epic Boss system instead.");
				player.sendMessage("Teleport to Epic Frintezza: .go -87784 -155090 -9080");
				// ReflectionUtils.enterReflection(player, new Frintezza(), INSTANCE_ID);
			}
		}
		else
		{
			super.onBypassFeedback(player, command);
		}
	}

	// High Rate Server: Removed scroll requirement methods
}
package bosses;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import bosses.EpicBossState.State;
import l2mv.commons.threading.RunnableImpl;
import l2mv.gameserver.Config;
import l2mv.gameserver.ThreadPoolManager;
import l2mv.gameserver.data.xml.holder.NpcHolder;
import l2mv.gameserver.listener.actor.OnDeathListener;
import l2mv.gameserver.model.Creature;
import l2mv.gameserver.model.Player;
import l2mv.gameserver.model.SimpleSpawner;
import l2mv.gameserver.model.Zone;
import l2mv.gameserver.model.actor.listener.CharListenerList;
import l2mv.gameserver.model.instances.BossInstance;
import l2mv.gameserver.model.instances.NpcInstance;
import l2mv.gameserver.network.serverpackets.PlaySound;
import l2mv.gameserver.scripts.Functions;
import l2mv.gameserver.scripts.ScriptFile;
import l2mv.gameserver.utils.Location;
import l2mv.gameserver.utils.Log;
import l2mv.gameserver.utils.ReflectionUtils;
import l2mv.gameserver.utils.TimeUtils;

/**
 * Epic Boss Manager for Queen Ant
 * Converts Queen Ant from Raid Boss to Epic Boss with full mechanics
 * Includes: Larva protection, Nurse healing, Royal Guards, automatic respawn
 */
public class QueenAntEpicManager extends Functions implements ScriptFile, OnDeathListener
{
	private static final Logger LOG = LoggerFactory.getLogger(QueenAntEpicManager.class);

	// Epic Boss Configuration
	private static final int QUEEN_ANT_ID = 29001;
	private static final int QUEEN_ANT_LARVA_ID = 29002;
	private static final int NURSE_ANT_ID = 29003;
	private static final int ROYAL_GUARD_ANT_ID = 29005;
	
	// Spawn Configuration (using original coordinates from spawn file)
	private static final Location QUEEN_ANT_SPAWN = new Location(-21610, 181594, -5734, 0);
	private static final Location LARVA_SPAWN = new Location(-21610, 181594, -5734, 0);

	// Minion spawn locations around Queen Ant (using exact same location as Queen Ant for testing)
	private static final Location[] NURSE_SPAWNS = {
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0)
	};

	private static final Location[] GUARD_SPAWNS = {
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0),
		new Location(-21610, 181594, -5734, 0)
	};
	
	// Timeout Configuration
	private static final long COMBAT_TIMEOUT = 30 * 60 * 1000L; // 30 minutes
	private static final long PARTY_WIPE_TIMEOUT = 5 * 60 * 1000L; // 5 minutes
	private static final long CHECK_INTERVAL = 60 * 1000L; // 1 minute
	
	// Respawn Configuration
	private static final long NURSE_RESPAWN_TIME = 10 * 1000L; // 10 seconds
	private static final long GUARD_RESPAWN_TIME = 280 * 1000L; // ~4.5 minutes
	
	// Epic Boss State
	private static EpicBossState _state;
	private static Zone _zone;
	
	// Boss Instances
	private static NpcInstance _queenAnt;
	private static NpcInstance _larva;
	
	// Minions
	private static final List<NpcInstance> _nurses = new ArrayList<>();
	private static final List<NpcInstance> _guards = new ArrayList<>();
	
	// Timers
	private static long _lastCombatTime = 0;
	private static long _lastPlayerTime = 0;
	private static ScheduledFuture<?> _timeoutTask;
	
	// Spawners
	private static SimpleSpawner _queenAntSpawner;
	private static SimpleSpawner _larvaSpawner;
	private static final List<SimpleSpawner> _nurseSpawners = new ArrayList<>();
	private static final List<SimpleSpawner> _guardSpawners = new ArrayList<>();
	
	// Instance reference for non-static methods
	private static QueenAntEpicManager _instance;

	@Override
	public void onLoad()
	{
		_instance = this;
		init();
	}

	private void init()
	{
		_state = new EpicBossState(QUEEN_ANT_ID);
		_zone = ReflectionUtils.getZone("[queen_ant_epic]");

		// Add global listener
		CharListenerList.addGlobal(this);

		// Initialize spawners
		initializeSpawners();
		
		// Check if state is null (first time initialization)
		if (_state.getState() == null)
		{
			LOG.info("QueenAntEpicManager: First time initialization, setting state to NOTSPAWN");
			_state.setState(State.NOTSPAWN);
			_state.update();
		}

		LOG.info("QueenAntEpicManager: State of Queen Ant is " + _state.getState() + ".");

		// Handle initial state
		if (_state.getState().equals(State.NOTSPAWN))
		{
			spawnQueenAnt();
		}
		else if (_state.getState().equals(State.ALIVE))
		{
			// Boss is alive, reset to initial state
			_state.setState(State.NOTSPAWN);
			_state.update();
			spawnQueenAnt();
		}
		else if (_state.getState().equals(State.INTERVAL) || _state.getState().equals(State.DEAD))
		{
			setIntervalEndTask();
		}
		
		// Start timeout monitoring
		startTimeoutMonitoring();
		
		LOG.info("QueenAntEpicManager: Next spawn date: " + TimeUtils.toSimpleFormat(_state.getRespawnDate()));
	}

	private void initializeSpawners()
	{
		try
		{
			// Queen Ant spawner
			_queenAntSpawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(QUEEN_ANT_ID));
			_queenAntSpawner.setAmount(1);
			_queenAntSpawner.setLoc(QUEEN_ANT_SPAWN);
			_queenAntSpawner.stopRespawn();

			// Larva spawner
			_larvaSpawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(QUEEN_ANT_LARVA_ID));
			_larvaSpawner.setAmount(1);
			_larvaSpawner.setLoc(LARVA_SPAWN);
			_larvaSpawner.stopRespawn();

			// Nurse spawners
			for (Location loc : NURSE_SPAWNS)
			{
				if (NpcHolder.getInstance().getTemplate(NURSE_ANT_ID) == null)
				{
					LOG.error("Nurse Ant template not found! ID: " + NURSE_ANT_ID);
					continue;
				}

				SimpleSpawner spawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(NURSE_ANT_ID));
				spawner.setAmount(1);
				spawner.setLoc(loc);
				spawner.stopRespawn();
				_nurseSpawners.add(spawner);
				LOG.info("Nurse spawner created at: " + loc);
			}

			// Guard spawners
			for (Location loc : GUARD_SPAWNS)
			{
				if (NpcHolder.getInstance().getTemplate(ROYAL_GUARD_ANT_ID) == null)
				{
					LOG.error("Royal Guard Ant template not found! ID: " + ROYAL_GUARD_ANT_ID);
					continue;
				}

				SimpleSpawner spawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(ROYAL_GUARD_ANT_ID));
				spawner.setAmount(1);
				spawner.setLoc(loc);
				spawner.stopRespawn();
				_guardSpawners.add(spawner);
				LOG.info("Guard spawner created at: " + loc);
			}

			LOG.info("Queen Ant spawners initialized: " + _nurseSpawners.size() + " nurses, " + _guardSpawners.size() + " guards");
		}
		catch (Exception e)
		{
			LOG.error("Error initializing Queen Ant spawners", e);
		}
	}

	private void spawnQueenAnt()
	{
		try
		{
			// Spawn Queen Ant
			_queenAnt = _queenAntSpawner.doSpawn(true);
			_queenAnt.addListener(this);
			
			// Spawn Larva (immortal)
			_larva = _larvaSpawner.doSpawn(true);
			_larva.addListener(this);
			_larva.setCurrentHp(1.0, false); // Larva cannot die (minimum 1 HP)
			
			// Spawn Nurses
			spawnNurses();
			
			// Spawn Guards
			spawnGuards();
			
			// Set state
			_state.setState(State.ALIVE);
			_state.update();
			
			// Update timers
			updateCombatTime();
			updatePlayerTime();
			
			Log.add("Queen Ant Epic Boss spawned with all minions", "bosses");
			
			// Broadcast spawn sound
			_queenAnt.broadcastPacket(new PlaySound(PlaySound.Type.MUSIC, "BS01_A", 1, 0, _queenAnt.getLoc()));
		}
		catch (Exception e)
		{
			LOG.error("Error spawning Queen Ant", e);
		}
	}

	private void spawnNurses()
	{
		try
		{
			_nurses.clear();
			for (SimpleSpawner spawner : _nurseSpawners)
			{
				if (spawner == null)
				{
					LOG.error("Nurse spawner is null!");
					continue;
				}

				LOG.info("Attempting to spawn nurse at: " + spawner.getLoc());
				NpcInstance nurse = spawner.doSpawn(true);
				if (nurse == null)
				{
					LOG.error("Failed to spawn nurse at location: " + spawner.getLoc());
					LOG.error("Spawner NPC ID: " + NURSE_ANT_ID);
					LOG.error("Spawner amount: " + spawner.getAmount());
					continue;
				}
				LOG.info("Successfully spawned nurse: " + nurse.getName() + " at " + nurse.getLoc());

				nurse.addListener(this);
				_nurses.add(nurse);
			}

			Log.add("Queen Ant nurses spawned: " + _nurses.size(), "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error spawning Queen Ant nurses", e);
		}
	}

	private void spawnGuards()
	{
		try
		{
			_guards.clear();
			for (SimpleSpawner spawner : _guardSpawners)
			{
				if (spawner == null)
				{
					LOG.error("Guard spawner is null!");
					continue;
				}

				NpcInstance guard = spawner.doSpawn(true);
				if (guard == null)
				{
					LOG.error("Failed to spawn guard at location: " + spawner.getLoc());
					continue;
				}

				guard.addListener(this);
				_guards.add(guard);
			}

			Log.add("Queen Ant guards spawned: " + _guards.size(), "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error spawning Queen Ant guards", e);
		}
	}

	private void onQueenAntDeath()
	{
		// Epic Boss completely defeated
		_state.setRespawnDate(getRespawnInterval());
		_state.setState(State.INTERVAL);
		_state.update();
		
		// Clean up everything
		cleanupAll();
		
		// Start respawn timer
		setIntervalEndTask();
		
		Log.add("Queen Ant Epic Boss defeated", "bosses");
		
		// Broadcast death sound
		if (_queenAnt != null)
		{
			_queenAnt.broadcastPacket(new PlaySound(PlaySound.Type.MUSIC, "BS02_D", 1, 0, _queenAnt.getLoc()));
		}
	}

	private void onNurseDeath(NpcInstance nurse)
	{
		// Respawn nurse after 10 seconds
		ThreadPoolManager.getInstance().schedule(new RespawnNurse(nurse), NURSE_RESPAWN_TIME);
		updateCombatTime();
	}

	private void onGuardDeath(NpcInstance guard)
	{
		// Respawn guard after ~4.5 minutes
		ThreadPoolManager.getInstance().schedule(new RespawnGuard(guard), GUARD_RESPAWN_TIME);
		updateCombatTime();
	}

	private static class RespawnNurse extends RunnableImpl
	{
		private final NpcInstance _deadNurse;
		
		public RespawnNurse(NpcInstance deadNurse)
		{
			_deadNurse = deadNurse;
		}
		
		@Override
		public void runImpl() throws Exception
		{
			if (_state.getState().equals(State.ALIVE) && _instance != null)
			{
				// Find the spawner for this nurse location
				Location deadLoc = _deadNurse.getSpawnedLoc();
				for (SimpleSpawner spawner : _nurseSpawners)
				{
					if (spawner.getLoc().equals(deadLoc))
					{
						NpcInstance newNurse = spawner.doSpawn(true);
						newNurse.addListener(_instance);
						_nurses.add(newNurse);
						Log.add("Queen Ant nurse respawned", "bosses");
						break;
					}
				}
			}
		}
	}

	private static class RespawnGuard extends RunnableImpl
	{
		private final NpcInstance _deadGuard;
		
		public RespawnGuard(NpcInstance deadGuard)
		{
			_deadGuard = deadGuard;
		}
		
		@Override
		public void runImpl() throws Exception
		{
			if (_state.getState().equals(State.ALIVE) && _instance != null)
			{
				// Find the spawner for this guard location
				Location deadLoc = _deadGuard.getSpawnedLoc();
				for (SimpleSpawner spawner : _guardSpawners)
				{
					if (spawner.getLoc().equals(deadLoc))
					{
						NpcInstance newGuard = spawner.doSpawn(true);
						newGuard.addListener(_instance);
						_guards.add(newGuard);
						Log.add("Queen Ant guard respawned", "bosses");
						break;
					}
				}
			}
		}
	}

	private void cleanupAll()
	{
		// Despawn all minions
		for (NpcInstance nurse : _nurses)
		{
			if (nurse != null && !nurse.isDead())
			{
				nurse.deleteMe();
			}
		}
		_nurses.clear();

		for (NpcInstance guard : _guards)
		{
			if (guard != null && !guard.isDead())
			{
				guard.deleteMe();
			}
		}
		_guards.clear();

		// Clear boss references
		if (_larva != null && !_larva.isDead())
		{
			_larva.deleteMe();
		}
		_larva = null;
		_queenAnt = null;

		// Reset timers
		_lastCombatTime = 0;
		_lastPlayerTime = 0;
	}

	private void resetToInitialState()
	{
		Log.add("Resetting Queen Ant to initial state", "bosses");

		// Clean up current state
		cleanupAll();

		// Reset state if needed
		if (!_state.getState().equals(State.NOTSPAWN))
		{
			_state.setState(State.NOTSPAWN);
			_state.update();
		}
	}

	private void updateCombatTime()
	{
		_lastCombatTime = System.currentTimeMillis();
	}

	private void updatePlayerTime()
	{
		_lastPlayerTime = System.currentTimeMillis();
	}

	private long getRespawnInterval()
	{
		return (long) (Config.ALT_RAID_RESPAWN_MULTIPLIER *
					  (Config.QUEEN_ANT_DEFAULT_SPAWN_HOURS * 60 * 60000L +
					   Math.random() * Config.QUEEN_ANT_RANDOM_SPAWN_HOURS * 60 * 60000L));
	}

	private void setIntervalEndTask()
	{
		if (_state.getState().equals(State.INTERVAL))
		{
			ThreadPoolManager.getInstance().schedule(new IntervalEnd(), _state.getInterval());
		}
	}

	private static class IntervalEnd extends RunnableImpl
	{
		@Override
		public void runImpl() throws Exception
		{
			_state.setState(State.NOTSPAWN);
			_state.update();

			// Spawn Queen Ant
			if (_instance != null)
			{
				_instance.spawnQueenAnt();
			}
		}
	}

	private void startTimeoutMonitoring()
	{
		_timeoutTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(new TimeoutCheck(),
																		   CHECK_INTERVAL,
																		   CHECK_INTERVAL);
	}

	private static class TimeoutCheck extends RunnableImpl
	{
		@Override
		public void runImpl() throws Exception
		{
			// Only check if boss is alive
			if (!_state.getState().equals(State.ALIVE))
				return;

			long currentTime = System.currentTimeMillis();

			// Check combat timeout
			if (_lastCombatTime > 0 && currentTime - _lastCombatTime > COMBAT_TIMEOUT)
			{
				Log.add("Queen Ant combat timeout - resetting to initial state", "bosses");
				if (_instance != null)
				{
					_instance.resetToInitialState();
					_instance.spawnQueenAnt();
				}
				return;
			}

			// Check party wipe
			if (_zone != null)
			{
				List<Player> alivePlayers = new ArrayList<>();
				for (Player player : _zone.getInsidePlayers())
				{
					if (!player.isDead())
					{
						alivePlayers.add(player);
					}
				}

				if (alivePlayers.isEmpty())
				{
					if (_lastPlayerTime > 0 && currentTime - _lastPlayerTime > PARTY_WIPE_TIMEOUT)
					{
						Log.add("Queen Ant party wipe timeout - resetting to initial state", "bosses");
						if (_instance != null)
						{
							_instance.resetToInitialState();
							_instance.spawnQueenAnt();
						}
					}
				}
				else
				{
					if (_instance != null)
					{
						_instance.updatePlayerTime();
					}
				}
			}
		}
	}

	@Override
	public void onDeath(Creature actor, Creature killer)
	{
		if (actor == null || !actor.isNpc())
			return;

		int npcId = actor.getNpcId();
		updateCombatTime();

		// Handle Queen Ant death
		if (npcId == QUEEN_ANT_ID)
		{
			onQueenAntDeath();
		}
		// Handle Nurse death
		else if (npcId == NURSE_ANT_ID)
		{
			_nurses.remove(actor);
			onNurseDeath((NpcInstance) actor);
		}
		// Handle Guard death
		else if (npcId == ROYAL_GUARD_ANT_ID)
		{
			_guards.remove(actor);
			onGuardDeath((NpcInstance) actor);
		}
		// Larva cannot die (handled by AI)
	}

	public static EpicBossState getState()
	{
		return _state;
	}



	@Override
	public void onReload()
	{
		if (_instance != null)
		{
			_instance.resetToInitialState();
		}
	}

	@Override
	public void onShutdown()
	{
		if (_timeoutTask != null)
		{
			_timeoutTask.cancel(false);
		}
	}
}

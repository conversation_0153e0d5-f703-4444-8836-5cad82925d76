package bosses;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import bosses.EpicBossState.State;
import l2mv.commons.threading.RunnableImpl;
import l2mv.gameserver.Config;
import l2mv.gameserver.ThreadPoolManager;
import l2mv.gameserver.data.xml.holder.NpcHolder;
import l2mv.gameserver.listener.actor.OnDeathListener;
import l2mv.gameserver.model.Creature;
import l2mv.gameserver.model.Player;
import l2mv.gameserver.model.SimpleSpawner;
import l2mv.gameserver.model.Zone;
import l2mv.gameserver.model.actor.listener.CharListenerList;
import l2mv.gameserver.model.instances.BossInstance;
import l2mv.gameserver.model.instances.NpcInstance;
import l2mv.gameserver.network.serverpackets.PlaySound;
import l2mv.gameserver.scripts.Functions;
import l2mv.gameserver.scripts.ScriptFile;
import l2mv.gameserver.utils.Location;
import l2mv.gameserver.utils.Log;
import l2mv.gameserver.utils.ReflectionUtils;
import l2mv.gameserver.utils.TimeUtils;

/**
 * Epic Boss Manager for Frintezza
 * Converts Frintezza from Instance Boss to Epic Boss with full mechanics
 * Includes: Portraits, Demons, 3-Phase system, Timeout, Party Wipe Detection
 */
public class FrintezzaEpicManager extends Functions implements ScriptFile, OnDeathListener
{
	private static final Logger LOG = LoggerFactory.getLogger(FrintezzaEpicManager.class);

	// Epic Boss Configuration
	private static final int FRINTEZZA_ID = 29045;
	private static final int WEAK_SCARLET_ID = 29046;
	private static final int STRONG_SCARLET_ID = 29047;
	
	// Portraits and Demons
	private static final int PORTRAIT_1 = 29048;
	private static final int PORTRAIT_2 = 29049;
	private static final int DEMON_1 = 29050;
	private static final int DEMON_2 = 29051;
	
	// Locations
	private static final Location FRINTEZZA_SPAWN = new Location(-87784, -155090, -9080, 16048);
	private static final Location[] PORTRAIT_SPAWNS = {
		new Location(-86136, -153960, -9168, 35048),
		new Location(-86184, -152456, -9168, 28205),
		new Location(-89368, -152456, -9168, 64817),
		new Location(-89416, -153976, -9168, 57730)
	};
	
	// Timeout Configuration
	private static final long COMBAT_TIMEOUT = 30 * 60 * 1000L; // 30 minutes
	private static final long PARTY_WIPE_TIMEOUT = 5 * 60 * 1000L; // 5 minutes
	private static final long CHECK_INTERVAL = 60 * 1000L; // 1 minute
	
	// Epic Boss State
	private static EpicBossState _state;
	private static Zone _zone;
	
	// Boss Instances
	private static NpcInstance _frintezza;
	private static NpcInstance _weakScarlet;
	private static NpcInstance _strongScarlet;
	
	// Minions
	private static final List<NpcInstance> _portraits = new ArrayList<>();
	private static final List<NpcInstance> _demons = new ArrayList<>();
	
	// State Management
	public enum FrintezzaPhase {
		INITIAL,        // Frintezza + Portraits
		PORTRAITS_DEAD, // Frintezza + Demons  
		FRINTEZZA_DEAD, // Weak Scarlet
		WEAK_DEAD,      // Strong Scarlet
		COMPLETED       // Dead, awaiting respawn
	}
	
	private static FrintezzaPhase _currentPhase = FrintezzaPhase.INITIAL;
	
	// Timers
	private static long _lastCombatTime = 0;
	private static long _lastPlayerTime = 0;
	private static ScheduledFuture<?> _timeoutTask;
	
	// Spawners
	private static SimpleSpawner _frintezzaSpawner;
	private static SimpleSpawner _weakScarletSpawner;
	private static SimpleSpawner _strongScarletSpawner;
	private static final List<SimpleSpawner> _portraitSpawners = new ArrayList<>();
	private static final List<SimpleSpawner> _demonSpawners = new ArrayList<>();
	
	// Instance reference for non-static methods
	private static FrintezzaEpicManager _instance;

	@Override
	public void onLoad()
	{
		_instance = this;
		init();
	}

	private void init()
	{
		_state = new EpicBossState(FRINTEZZA_ID);
		_zone = ReflectionUtils.getZone("[Frintezza]");

		// Add global listener
		CharListenerList.addGlobal(this);

		// Initialize spawners
		initializeSpawners();
		
		// Check if state is null (first time initialization)
		if (_state.getState() == null)
		{
			LOG.info("FrintezzaEpicManager: First time initialization, setting state to NOTSPAWN");
			_state.setState(State.NOTSPAWN);
			_state.update();
		}

		LOG.info("FrintezzaEpicManager: State of Frintezza is " + _state.getState() + ".");

		// Handle initial state
		if (_state.getState().equals(State.NOTSPAWN))
		{
			spawnInitialFrintezza();
		}
		else if (_state.getState().equals(State.ALIVE))
		{
			// Boss is alive, reset to initial state
			_state.setState(State.NOTSPAWN);
			_state.update();
			spawnInitialFrintezza();
		}
		else if (_state.getState().equals(State.INTERVAL) || _state.getState().equals(State.DEAD))
		{
			setIntervalEndTask();
		}
		
		// Start timeout monitoring
		startTimeoutMonitoring();
		
		LOG.info("FrintezzaEpicManager: Next spawn date: " + TimeUtils.toSimpleFormat(_state.getRespawnDate()));
	}

	private void initializeSpawners()
	{
		try
		{
			// Frintezza spawner
			_frintezzaSpawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(FRINTEZZA_ID));
			_frintezzaSpawner.setAmount(1);
			_frintezzaSpawner.setLoc(FRINTEZZA_SPAWN);
			_frintezzaSpawner.stopRespawn();
			
			// Weak Scarlet spawner
			_weakScarletSpawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(WEAK_SCARLET_ID));
			_weakScarletSpawner.setAmount(1);
			_weakScarletSpawner.setLoc(FRINTEZZA_SPAWN);
			_weakScarletSpawner.stopRespawn();
			
			// Strong Scarlet spawner
			_strongScarletSpawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(STRONG_SCARLET_ID));
			_strongScarletSpawner.setAmount(1);
			_strongScarletSpawner.setLoc(FRINTEZZA_SPAWN);
			_strongScarletSpawner.stopRespawn();
			
			// Portrait spawners
			for (int i = 0; i < 4; i++)
			{
				int npcId = (i % 2 == 0) ? PORTRAIT_1 : PORTRAIT_2;
				SimpleSpawner spawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(npcId));
				spawner.setAmount(1);
				spawner.setLoc(PORTRAIT_SPAWNS[i]);
				spawner.stopRespawn();
				_portraitSpawners.add(spawner);
			}
			
			// Demon spawners
			for (int i = 0; i < 4; i++)
			{
				int npcId = (i % 2 == 0) ? DEMON_1 : DEMON_2;
				SimpleSpawner spawner = new SimpleSpawner(NpcHolder.getInstance().getTemplate(npcId));
				spawner.setAmount(1);
				spawner.setLoc(PORTRAIT_SPAWNS[i]);
				spawner.stopRespawn();
				_demonSpawners.add(spawner);
			}
		}
		catch (Exception e)
		{
			LOG.error("Error initializing Frintezza spawners", e);
		}
	}

	private void spawnInitialFrintezza()
	{
		if (_currentPhase != FrintezzaPhase.INITIAL)
		{
			resetToInitialState();
		}
		
		try
		{
			// Spawn Frintezza (invulnerable initially)
			LOG.info("Attempting to spawn Frintezza at: " + FRINTEZZA_SPAWN);
			_frintezza = _frintezzaSpawner.doSpawn(true);
			if (_frintezza == null)
			{
				LOG.error("Failed to spawn Frintezza at location: " + FRINTEZZA_SPAWN);
				return;
			}
			LOG.info("Successfully spawned Frintezza: " + _frintezza.getName() + " at " + _frintezza.getLoc());
			_frintezza.setIsInvul(true);
			_frintezza.addListener(this);
			
			// Spawn Portraits
			spawnPortraits();
			
			// Set state
			_state.setState(State.ALIVE);
			_state.update();
			_currentPhase = FrintezzaPhase.INITIAL;
			
			// Update timers
			updateCombatTime();
			updatePlayerTime();
			
			Log.add("Frintezza Epic Boss spawned in initial state", "bosses");
			
			// Broadcast spawn sound
			_frintezza.broadcastPacket(new PlaySound(PlaySound.Type.MUSIC, "BS01_A", 1, 0, _frintezza.getLoc()));
		}
		catch (Exception e)
		{
			LOG.error("Error spawning initial Frintezza", e);
		}
	}

	private void spawnPortraits()
	{
		try
		{
			_portraits.clear();
			for (SimpleSpawner spawner : _portraitSpawners)
			{
				NpcInstance portrait = spawner.doSpawn(true);
				portrait.addListener(this);
				_portraits.add(portrait);
			}
			
			Log.add("Frintezza portraits spawned: " + _portraits.size(), "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error spawning Frintezza portraits", e);
		}
	}

	private void spawnDemons()
	{
		try
		{
			_demons.clear();
			for (SimpleSpawner spawner : _demonSpawners)
			{
				NpcInstance demon = spawner.doSpawn(true);
				demon.addListener(this);
				_demons.add(demon);
			}

			Log.add("Frintezza demons spawned: " + _demons.size(), "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error spawning Frintezza demons", e);
		}
	}

	private void onAllPortraitsDead()
	{
		if (_currentPhase != FrintezzaPhase.INITIAL)
			return;

		// Remove Frintezza invulnerability
		if (_frintezza != null && !_frintezza.isDead())
		{
			_frintezza.setIsInvul(false);
		}

		// Spawn demons
		spawnDemons();

		// Update phase
		_currentPhase = FrintezzaPhase.PORTRAITS_DEAD;
		updateCombatTime();

		Log.add("All Frintezza portraits dead, demons spawned", "bosses");
	}

	private void onFrintezzaDeath()
	{
		if (_currentPhase != FrintezzaPhase.PORTRAITS_DEAD)
			return;

		try
		{
			// Despawn demons
			despawnDemons();

			// Spawn Weak Scarlet at same location
			_weakScarletSpawner.setLoc(_frintezza.getLoc());
			_weakScarlet = _weakScarletSpawner.doSpawn(true);
			_weakScarlet.addListener(this);

			// Update phase
			_currentPhase = FrintezzaPhase.FRINTEZZA_DEAD;
			updateCombatTime();

			Log.add("Frintezza dead, Weak Scarlet spawned", "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error handling Frintezza death", e);
		}
	}

	private void onWeakScarletDeath()
	{
		if (_currentPhase != FrintezzaPhase.FRINTEZZA_DEAD)
			return;

		try
		{
			// Spawn Strong Scarlet at same location
			_strongScarletSpawner.setLoc(_weakScarlet.getLoc());
			_strongScarlet = _strongScarletSpawner.doSpawn(true);
			_strongScarlet.addListener(this);

			// Update phase
			_currentPhase = FrintezzaPhase.WEAK_DEAD;
			updateCombatTime();

			Log.add("Weak Scarlet dead, Strong Scarlet spawned", "bosses");
		}
		catch (Exception e)
		{
			LOG.error("Error handling Weak Scarlet death", e);
		}
	}

	private void onStrongScarletDeath()
	{
		if (_currentPhase != FrintezzaPhase.WEAK_DEAD)
			return;

		// Epic Boss completely defeated
		_currentPhase = FrintezzaPhase.COMPLETED;

		// Set respawn timer
		_state.setRespawnDate(getRespawnInterval());
		_state.setState(State.INTERVAL);
		_state.update();

		// Clean up everything
		cleanupAll();

		// Start respawn timer
		setIntervalEndTask();

		Log.add("Strong Scarlet dead, Frintezza Epic Boss defeated", "bosses");

		// Broadcast death sound
		if (_strongScarlet != null)
		{
			_strongScarlet.broadcastPacket(new PlaySound(PlaySound.Type.MUSIC, "BS02_D", 1, 0, _strongScarlet.getLoc()));
		}
	}

	private void despawnDemons()
	{
		for (NpcInstance demon : _demons)
		{
			if (demon != null && !demon.isDead())
			{
				demon.deleteMe();
			}
		}
		_demons.clear();
	}

	private void cleanupAll()
	{
		// Despawn all minions
		for (NpcInstance portrait : _portraits)
		{
			if (portrait != null && !portrait.isDead())
			{
				portrait.deleteMe();
			}
		}
		_portraits.clear();

		despawnDemons();

		// Clear boss references
		_frintezza = null;
		_weakScarlet = null;
		_strongScarlet = null;

		// Reset timers
		_lastCombatTime = 0;
		_lastPlayerTime = 0;
	}

	private void resetToInitialState()
	{
		Log.add("Resetting Frintezza to initial state", "bosses");

		// Clean up current state
		cleanupAll();

		// Reset phase
		_currentPhase = FrintezzaPhase.INITIAL;

		// Reset state if needed
		if (!_state.getState().equals(State.NOTSPAWN))
		{
			_state.setState(State.NOTSPAWN);
			_state.update();
		}
	}

	private void updateCombatTime()
	{
		_lastCombatTime = System.currentTimeMillis();
	}

	private void updatePlayerTime()
	{
		_lastPlayerTime = System.currentTimeMillis();
	}

	private long getRespawnInterval()
	{
		return (long) (Config.ALT_RAID_RESPAWN_MULTIPLIER *
					  (Config.FRINTEZZA_DEFAULT_SPAWN_HOURS * 60 * 60000L +
					   Math.random() * Config.FRINTEZZA_RANDOM_SPAWN_HOURS * 60 * 60000L));
	}

	private void setIntervalEndTask()
	{
		if (_state.getState().equals(State.INTERVAL))
		{
			ThreadPoolManager.getInstance().schedule(new IntervalEnd(), _state.getInterval());
		}
	}

	private static class IntervalEnd extends RunnableImpl
	{
		@Override
		public void runImpl() throws Exception
		{
			_state.setState(State.NOTSPAWN);
			_state.update();

			// Spawn initial Frintezza
			if (_instance != null)
			{
				_instance.spawnInitialFrintezza();
			}
		}
	}

	private void startTimeoutMonitoring()
	{
		_timeoutTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(new TimeoutCheck(),
																		   CHECK_INTERVAL,
																		   CHECK_INTERVAL);
	}

	private static class TimeoutCheck extends RunnableImpl
	{
		@Override
		public void runImpl() throws Exception
		{
			// Only check if boss is alive
			if (!_state.getState().equals(State.ALIVE))
				return;

			long currentTime = System.currentTimeMillis();

			// Check combat timeout
			if (_lastCombatTime > 0 && currentTime - _lastCombatTime > COMBAT_TIMEOUT)
			{
				Log.add("Frintezza combat timeout - resetting to initial state", "bosses");
				if (_instance != null)
				{
					_instance.resetToInitialState();
					_instance.spawnInitialFrintezza();
				}
				return;
			}

			// Check party wipe
			if (_zone != null)
			{
				List<Player> alivePlayers = new ArrayList<>();
				for (Player player : _zone.getInsidePlayers())
				{
					if (!player.isDead())
					{
						alivePlayers.add(player);
					}
				}

				if (alivePlayers.isEmpty())
				{
					if (_lastPlayerTime > 0 && currentTime - _lastPlayerTime > PARTY_WIPE_TIMEOUT)
					{
						Log.add("Frintezza party wipe timeout - resetting to initial state", "bosses");
						if (_instance != null)
						{
							_instance.resetToInitialState();
							_instance.spawnInitialFrintezza();
						}
					}
				}
				else
				{
					if (_instance != null)
					{
						_instance.updatePlayerTime();
					}
				}
			}
		}
	}

	@Override
	public void onDeath(Creature actor, Creature killer)
	{
		if (actor == null || !actor.isNpc())
			return;

		int npcId = actor.getNpcId();
		updateCombatTime();

		// Handle portrait deaths
		if (npcId == PORTRAIT_1 || npcId == PORTRAIT_2)
		{
			// Check if all portraits are dead
			boolean allPortraitsDead = true;
			for (NpcInstance portrait : _portraits)
			{
				if (portrait != null && !portrait.isDead())
				{
					allPortraitsDead = false;
					break;
				}
			}

			if (allPortraitsDead)
			{
				onAllPortraitsDead();
			}
		}
		// Handle boss deaths
		else if (npcId == FRINTEZZA_ID)
		{
			onFrintezzaDeath();
		}
		else if (npcId == WEAK_SCARLET_ID)
		{
			onWeakScarletDeath();
		}
		else if (npcId == STRONG_SCARLET_ID)
		{
			onStrongScarletDeath();
		}
	}

	public static EpicBossState getState()
	{
		return _state;
	}



	@Override
	public void onReload()
	{
		if (_instance != null)
		{
			_instance.resetToInitialState();
		}
	}

	@Override
	public void onShutdown()
	{
		if (_timeoutTask != null)
		{
			_timeoutTask.cancel(false);
		}
	}
}
